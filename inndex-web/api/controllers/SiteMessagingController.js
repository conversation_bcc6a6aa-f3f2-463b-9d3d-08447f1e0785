const dayjs = require('dayjs');
const _uniq = require('lodash/uniq');
const {
    isShadowUser,
    DEFAULT_PAGE_SIZE,
    EMAIL_NOTIFICATION_FEATURE_CODES,
} = sails.config.constants;
const {
    UserRevisionService: {getLatestUserRevision},
    ResponseService: {
        errorResponse,
        successResponse
    },
    DataProcessingService: {
        getUserFullName,
        populateUserRefs,
        attachProfilePicWithUsersInfo,
        populateProjectRefs,
        processVisitingRecords,
    },
    EmailService: {
        queueEmailNotifications,
    },
    HttpService,
    NotificationService: {
        queuePushNotifications,
        NOTIFICATION_CATEGORY,
    }
} = require('./../services');

const {
    SMValidator: {
        createOrUpdateRecord,
        markAsMessage,
        validateCompanyMessagingRecord,
    }
} = require('./../validators');
const { coreFn:{
    runReadQuery
}, siteMessageFn:{
    getProjectSiteMessages,
    getConsolidatedMessagesListFn,
    msg_type,
    getCompanySiteMessages,
    updateResentData,
    getRecipientsList,
    getMessageSendersList,
    getCombinedUnreadMessagesCountFn
}, companyFn:{
    getCompanyAccessibleProjects
}
} = require('../sql.fn');

const cleanRecord = (record) => {
    //sails.log.info("cleaning message files", record.msg_files);
    if (record.msg_files && record.msg_files.length) {
        record.msg_files = (record.msg_files || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    if (record.recipients && record.recipients.length) {
        record.recipients = (record.recipients || []).reduce((arr, value) => {
            if (value && (typeof value === "number")) {
                arr.push(value)
            } else if (value && (typeof value === "object") && value.id) {
                arr.push(value.id);
            }
            return arr;
        }, []);
    }

    return record;
};

const expandMessages = async (project_messages = []) => {
    let userIds = [];
    let fileIds = [];
    let profilePicIds = [];
    let userRefEmployerIds = [];
    for (let index in project_messages) {
        let messageInfo = project_messages[index];
        messageInfo.recipients && userIds.push(...messageInfo.recipients);
        fileIds.push(...messageInfo.msg_files);
        let user_file_id = messageInfo.user_ref && messageInfo.user_ref.profile_pic_ref || 0;
        profilePicIds.push(user_file_id);
        if ( messageInfo && messageInfo.user_ref && messageInfo.user_ref.parent_company ) {
            userRefEmployerIds.push(messageInfo.user_ref.parent_company);
        }
    }

    let usersInfo = await sails.models.user_reader.find({
        where: {'id': _uniq(userIds)},
        select: ['email', 'first_name', 'last_name', 'parent_company', 'profile_pic_ref']
    }).populate('parent_company');

    let userRefEmployer = await sails.models.createemployer_reader.find({
        where: {'id': _uniq(userRefEmployerIds)},
        select: ['name']
    });

    let recipientsProfilePic = usersInfo.map(u => u.profile_pic_ref).filter(a => a !== null );

    let filesInfo = await sails.models.userfile_reader.find({
        where: {id: [..._uniq(fileIds), ..._uniq(profilePicIds), ..._uniq(recipientsProfilePic)]},
        select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
    });

    const fileMap = new Map(filesInfo.map(f => [f.id, f]));

    for (let index in project_messages) {
        let messageInfo = project_messages[index];
        messageInfo.recipients = usersInfo.filter(user => messageInfo.recipients.includes(user.id));
        messageInfo.msg_files = filesInfo.filter(file => messageInfo.msg_files.includes(file.id));
        messageInfo.recipients = messageInfo.recipients.map(u => ({ ...u.toJSON(), profile_pic_ref: fileMap.get(u.profile_pic_ref) || null }));
        if(messageInfo && messageInfo.user_ref){
            messageInfo.user_ref.parent_company = userRefEmployer.find(emp => messageInfo.user_ref.parent_company == emp.id);
            let user_file_id = messageInfo.user_ref && messageInfo.user_ref.profile_pic_ref || 0;
            messageInfo.user_ref.profile_pic_ref = filesInfo.find(p => p.id === user_file_id) || null;
        }
        project_messages[index] = messageInfo;
    }

    return project_messages;
};

const sendMessageAsPushNotificationV2 = async (messageInfo, project, sent_by, recipientIds, company = {}) => {
    let notificationCategory = NOTIFICATION_CATEGORY.SITE_MESSAGING;
    let msg_text = `${sent_by.first_name} ${sent_by.last_name} has sent you a message for ${company.name || project.name} project.`;

    let profile_pic = null;

    if (sent_by.profile_pic_ref && HttpService.typeOf(sent_by.profile_pic_ref, "number")) {
        sent_by.profile_pic_ref = await sails.models.userfile_reader.findOne({
            where: { id: sent_by.profile_pic_ref },
            select: ['id', 'file_url']
        });
        profile_pic = sent_by.profile_pic_ref.file_url;
    }

    let messageBody = {
        category: notificationCategory,
        messageId: messageInfo.id.toString(),
        profile_pic: profile_pic || '',
    };

    if (company.name) {
        msg_text = `${sent_by.first_name} ${sent_by.last_name} has sent you a message for ${company.name} company.`;
        notificationCategory = NOTIFICATION_CATEGORY.COMPANY_MESSAGING;
        messageBody.category = notificationCategory;
        messageBody.company_ref = company.id.toString();
    } else {
        messageBody.project_ref = project.id.toString();
    }

    const pushNotification = { title: messageInfo.msg_title, body: msg_text };

    return await queuePushNotifications(pushNotification, messageBody, recipientIds);
};

const filterAndMergeArr = (newArr, existingArr) => {
    return [...newArr, ...existingArr].reduce((arr, item) => {
        let existingItem = arr.find(user => user.id == item.id);
        if(!existingItem) {
            arr.push(item);
        }
        return arr
    }, []);
};

/**
 *
 * @param project {id, name}
 * @param messageInfo {msg_title, msg_text}
 * @param recipients
 * @param sender
 * @param attachments?
 * @returns {Promise<boolean>}
 */
const queueMessageForEmailing = async (project, messageInfo, recipients, sender, attachments = []) => {
    let {id: projectId, name: projectName} = project;
    let {id: senderId} = sender;
    let {msg_title, msg_text} = messageInfo;
    let senderName = getUserFullName(sender);
    let [firstFileRef] = attachments;
    return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.SITE_MESSAGING, recipients.map(u => ({
        id: u.id,
        name: getUserFullName(u),
        email: u.email,
    })), {
        project: {
            id: projectId,
            name: projectName
        },
        sender: {
            id: senderId,
            name: senderName
        },
        messageInfo: {
            msg_title: msg_title,
            msg_lines: msg_text.split('\n'),
            msg_files: attachments.map(a => ({name: a.name, file_url: a.file_url})),
        },
    }, ( null ));
};
const queueCompanyMessageForEmailing = async (employer, messageInfo, recipients, sender, attachments = []) => {
    let {msg_title, msg_text} = messageInfo;
    let senderName = getUserFullName(sender);
    let payload = {
        msg_title: msg_title,
        msg_lines: msg_text.split('\n'),
    }
    if(attachments.length){
        payload.msg_files = attachments.map(a => ({name: a.name, file_url: a.file_url}));
    }
    return await queueEmailNotifications(EMAIL_NOTIFICATION_FEATURE_CODES.COMPANY_SITE_MESSAGING, recipients.map(u => ({
        id: u.id,
        name: getUserFullName(u),
        email: u.email,
    })), {
        company: {
            id:employer.id,
            name:employer.name
        },
        sender:{
            id:sender.id,
            name:senderName
        }  ,
        messageInfo: payload ,
    }, (null));
};

 const sendCompanyMessageFn = async (req, res) => {
    let employer_id = +req.param('employerId',0);
    let message_id = +req.param('messageId',0)

    let requestBody = _.pick((req.body || {}), ['msg_title', 'msg_text', 'msg_files', 'should_accepted', 'projects', 'last30_visited_user', 'today_visited_user', 'scheduled_at', 'is_sent', 'mail_alert']);
    sails.log.info(`Send message for company: ${employer_id}, by: ${req.user.id}, message_as_email ? ${requestBody.mail_alert}`);
    let {
        validationError,
        payload
    } = validateCompanyMessagingRecord(req);
    if (validationError) {
        return errorResponse(res, 'Request failed due to invalid data, Please try again.', {
            validationError
        });
    };
    requestBody.sender_ref = req.user.id;
    requestBody = cleanRecord(requestBody);
    requestBody.company_ref = +employer_id;

    let employer = await sails.models.createemployer_reader.findOne({
        id: employer_id
    }).select(['id','name']);
    let companyProjects = await getCompanyAccessibleProjects(employer_id, ['name', 'custom_field'], true, requestBody.projects);
    sails.log.info('Skipping disabled projects from accessible projects list.');
    companyProjects = companyProjects.filter(p => p.custom_field && (!p.custom_field.disable || !p.custom_field.disable.company_portal));
    requestBody.projects = companyProjects.map(a => a.id);
    let messageInfo;
    if (message_id) {
        messageInfo = await sails.models.sitemessage.updateOne({
            id: message_id,
            company_ref:employer_id,
        }).set(requestBody)
    } else {
        messageInfo = await sails.models.sitemessage.create(requestBody);
    }
    if (!requestBody.is_sent) {
        return successResponse(res, {
            message: requestBody.id ? "Message has been edited successfully" : "Message has been scheduled",
        });
    };
    if(!messageInfo){
        return errorResponse(res,{message:"Message Delivery Failed: Unable to send message at this time."})
    }
    messageInfo.user_ref = req.user;
    let index = 2;
    let variables = [ 2, 6, ...messageInfo.projects];
    let last30_visited_userId = [];
    let today_visited_userId = [];
    let visitingrecordsFilter = ` AND project_ref IN (${messageInfo.projects.map(() => `$${++index}`).join(', ')})`;
    if (requestBody.today_visited_user || requestBody.last30_visited_user) {
        const {
            today_visited_userId: today_ids,
            last30_visited_userId: last30_ids
        } = await processVisitingRecords(messageInfo.projects, []);
        last30_visited_userId = last30_ids;
        today_visited_userId = today_ids;


        let userIds = [];
        if (requestBody.today_visited_user) {
            userIds.push(...today_visited_userId);
        };
        if (requestBody.last30_visited_user) {
            userIds.push(...last30_visited_userId);
        };

        userIds = _uniq(userIds);
        sails.log.info('active user Ids: ',userIds);


        if(!userIds.length){
            await sails.models.sitemessage.destroyOne({
                id: messageInfo.id
            });
            return successResponse(res, {
                title:'Warning',
                message: "There are no recipients based on your filters. Please adjust and try again."
            });
        }

        visitingrecordsFilter += ` AND user_ref IN (${userIds.map(a => `$${++index}`) })`;
        variables.push(...userIds);
    };

    let sql = `SELECT DISTINCT
                u.id,
                u.email,
                u.first_name,
                u.last_name,
                u.middle_name
        FROM induction_request ir
        INNER JOIN users u ON ir.user_ref = u.id
        WHERE
            status_code IN ($1, $2)
            AND u.is_active = 1
        ${visitingrecordsFilter}
        group by u.id`;
    let rawResult = await sails.sendNativeQuery(sql, variables);
    let users = (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
    users = users.filter(a => {
        return(!isShadowUser(a) && a.id !== req.user.id)
    });
    sails.log.info("distinct users: ", users.map(a=>a.id))
    let usersLength = users.length;
    if (!usersLength) {
        await sails.models.sitemessage.destroyOne({
            id: messageInfo.id
        });
        return successResponse(res, {
            title:'Warning',
            message: "Unfortunately, no users match the selected criteria for sending a message. Please adjust your criteria."
        });
    };
    let msgFiles = await sails.models.userfile_reader.find({
        where: {
            id: _uniq(messageInfo.msg_files)
        },
        select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
    });
    let today = dayjs().unix();
    let recipientCreationData = users.map(a => {
        return {
            msg_ref: +messageInfo.id,
            user_ref: +a.id,
            sent_at: today
        }
    });
    await sails.models.sitemessagerecipient.createEach(recipientCreationData);
    await sendMessageAsPushNotificationV2(messageInfo, {}, req.user, users.map(u => u.id), employer);
    if (requestBody.mail_alert) {
        await queueCompanyMessageForEmailing(employer, messageInfo, users, req.user, (msgFiles));
    };
    return successResponse(res, {
        message: `Message has been sent to ${usersLength} users successfully`,
        users: usersLength
    });
};
const getCompanyMessagesFn = async(req,res,receivedOnly = false)=>{
    const employerId = +req.param('employerId',0);
    let pageNumber = +req.param('pageNumber', 0);
    let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
    let searchTerm = (req.param('search') !== null && req.param('search') !== '') ? req.param('search') : '';
    let senders = req.param('sender')? req.param('sender').split(",").map(a => +a) : [];
    let sortKey = req.param('sortKey' ,'id');
    let sortDir = req.param('sortDir' ,'desc');

    let defaultResponse = {records: [], totalCount: 0, q: searchTerm, pageSize, pageNumber,};
    let {
        total: total_record_count,
        records: company_messages
    } = await getCompanySiteMessages(employerId, pageSize, pageNumber * pageSize, sortKey, sortDir, {
        searchTerm,
        senders,
        ...(receivedOnly && {userId: req.user.id})
    }, );

    company_messages = await expandMessages(company_messages);
    let projectIds = [];
    for (let message of company_messages){
        projectIds.push(...message.projects);
    }
    let projectDetails = await sails.models.project_reader.find({
        where:{ id:_uniq(projectIds) },
        select:['id','name']
    });
    company_messages = company_messages.map(record => {
        let projects  = projectDetails.filter(project=> record.projects.includes(project.id));
        record.projects = projects;
        return record;
    });

    let responseObj = { ...defaultResponse, records: company_messages, totalCount: total_record_count, user_filter_data :[] };
    if (pageNumber == 0 && !searchTerm && !senders.length) {
        let user_filter_data = [];
        let sql = `SELECT id, concat_ws(' ',first_name, middle_name, last_name) AS name FROM users WHERE id IN (SELECT DISTINCT sender_ref FROM site_message WHERE  company_ref = ${employerId})`;
        user_filter_data = await runReadQuery(sql);
        return successResponse(res,{...responseObj,user_filter_data} );
    }
    return successResponse(res, responseObj);
};

const getCompanyMessageRecipientsDetailsFn = async ( message_id, separateLists = true, expandProfilePicData = false, type = null, user)  =>{

    const recipients = await getRecipientsList(message_id, type && type === 'received' ? [user] : [], expandProfilePicData, type);
    if(!recipients.length){
        return errorResponse(res,{message: "something went wrong, error fetching recipients data."});
    }
    if(!separateLists) {
        return recipients;
    }
    let {deliveredTo, readBy} = recipients.reduce((acc, recipient) => {
                    if (recipient.read_at !== null) {
                        acc.readBy.push(recipient);
                    } else {
                        acc.deliveredTo.push(recipient);
                    }
                    return acc;
                }, { readBy: [], deliveredTo: [] });

    return {deliveredTo, readBy}
};

module.exports = {
    sendMessage: async (req, res) => {
        let requestBody = _.pick((req.body || {}), [
            'msg_title',
            'msg_text',
            'msg_files',
            'recipients',
            'should_accepted',
            'project_ref',
            'scheduleDelivery',
            'scheduled_at',
            'is_sent',
            'mail_alert'
        ]);
        const message_as_email = {...requestBody}.mail_alert;
        sails.log.info(`Send message for project: ${requestBody.project_ref}, by: ${req.user.id}, message_as_email ? ${message_as_email}`);
        let {validationError, payload} = createOrUpdateRecord(req);
        if(validationError){
            return errorResponse(res, 'Request failed due to invalid data, Please try again.', {validationError});
        };

        requestBody.user_ref = req.user.id;
        let revision = await getLatestUserRevision(req.user.id);
        requestBody.user_revision_ref = revision.id;

        requestBody = cleanRecord(requestBody);
        let messageInfo = await sails.models.projectsitemessaging.create(requestBody);
        messageInfo.user_ref = req.user;
        if(messageInfo)  {
            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: messageInfo.project_ref},
                select: ['name', 'project_category', 'parent_company', 'project_type', 'main_contact_name', 'main_contact_number']
            });
            //Excluding sender from recipient list
            const recipientIndex = (messageInfo.recipients || []).indexOf(req.user.id);
            if (recipientIndex > -1) {
                messageInfo.recipients.splice(recipientIndex, 1);
            }

            let recipientsInfo = await sails.models.user_reader.find({
                where: {'id': messageInfo.recipients, is_active: 1},
                select: ['email', 'first_name', 'last_name']
            });
            recipientsInfo = recipientsInfo.filter(u => !isShadowUser(u));

            if(message_as_email){
                let msgFiles = await sails.models.userfile_reader.find({
                    where: {id: _uniq(messageInfo.msg_files)},
                    select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime'],
                    sort: ['id']
                });
                sails.log.info('queuing email to recipients, count:', recipientsInfo.length);
                await queueMessageForEmailing(projectInfo, messageInfo, recipientsInfo, req.user, msgFiles);
            }
            await sendMessageAsPushNotificationV2(messageInfo, projectInfo, req.user, recipientsInfo.map(u => u.id));
            sails.log.info('Message stored successfully.');
            return successResponse(res, {messageInfo});
        }

        sails.log.info('Failed to send/save message.');
        return errorResponse(res, 'Failed to send/save message.');
    },

    resendMessage: async (req, res) => {
        let projectId = +req.param('projectId',0);
        sails.log.info('Resending message for project, by', req.user.id);
        let requestBody = _.pick((req.body || {}), [
            'message_id',
            'recipient_id',
        ]);

        if(!requestBody.message_id || !requestBody.recipient_id || (HttpService.typeOf(requestBody.recipient_id, 'array') && !requestBody.recipient_id.length)){
            return errorResponse(res,"Invalid request.");
        }

        let messageInfo = await sails.models.projectsitemessaging_reader.findOne({
            where: {
                id: requestBody.message_id,
                project_ref: projectId
            },
            select: ['msg_title', 'msg_text', 'msg_files', 'project_ref', 'resend_logs']
        });
        messageInfo.user_ref = req.user;
        if(messageInfo)  {
            let projectInfo = await sails.models.project_reader.findOne({
                where: {id: messageInfo.project_ref},
                select: ['name','project_category','parent_company']
            });
            let recipientInfo = await sails.models.user_reader.find({
                where: {'id': requestBody.recipient_id},
                select: ['id']
            });

            sails.log.info(`Resent push notification to ${recipientInfo.length} users`, );
            await sendMessageAsPushNotificationV2(messageInfo, projectInfo, req.user, recipientInfo.map(u=>+u.id));
            let payload = {
                resend_logs: [
                    ...messageInfo.resend_logs,
                    {
                        id: requestBody.recipient_id,
                        resend_at: dayjs().valueOf()
                    }
                ]
            }
            sails.log.info('Adding resend log with payload: ', payload);
            messageInfo = await sails.models.projectsitemessaging.updateOne({id: messageInfo.id}).set(payload);

            sails.log.info(`Notifications has been re-sent to recipient.`);

            return successResponse(res, {messageInfo});
        }

        sails.log.info('Failed to resend message.');
        return errorResponse(res, 'Failed to resend message.');
    },
    markAsMessage: async (req, res) => {
        sails.log.info('Update message for project, by', req.user.id);
        let markAs = req.param('markas');
        if (!['read', 'accepted', 'deleted'].includes(markAs)) {
            sails.log.info('Invalid request.');
            return errorResponse(res, 'Invalid request.');
        }

        let existingMessageInfo = await sails.models.projectsitemessaging_reader.findOne({
            where: {id: +req.param('id')},
            select: ['read_by_recipients', 'accepted_by_recipients', 'mark_deleted', 'recipients']
        });

        let {validationError, payload} = markAsMessage(req, markAs);
        if(validationError) {
            return ResponseService.errorResponse(res, 'Request failed due to invalid data, Please try again.', {validationError});
        }

        //Temporary log
        let invalidRecipients = (payload.read_by_recipients || payload.accepted_by_recipients || []).filter(recipient => !existingMessageInfo.recipients.includes(recipient.id));
        if (invalidRecipients.length) {
            sails.log.error(`Site Messaging: Got invalid ${markAs} request, Invalid Users: `, invalidRecipients, `, requesting user ${req.user.id}`);
        }

        if (markAs === 'read') {
            payload.read_by_recipients = filterAndMergeArr(payload.read_by_recipients, existingMessageInfo.read_by_recipients);
        } else if(markAs === 'accepted') {
            payload.accepted_by_recipients = filterAndMergeArr(payload.accepted_by_recipients, existingMessageInfo.accepted_by_recipients);
        }

        let messageInfo = await sails.models.projectsitemessaging.updateOne({id: +req.param('id')}).set(payload);

        if(messageInfo && messageInfo.id) {
            sails.log.info('message updated successfully, id', messageInfo ? messageInfo.id : undefined);
            return ResponseService.successResponse(res, {messageInfo});
        }

        sails.log.info('Failed to update message.');
        return errorResponse(res, 'Failed to update message.');
    },

    getProjectMessages: async (req, res) => {
        let projectId = +req.param('projectId');
        let recipientId = +req.param('recipientId');
        let filter = { project_ref: projectId };
        let pageNumber = +req.param('pageNumber', 0);
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let searchTerm = (req.param('search') !== null && req.param('search') !== '') ? req.param('search') : '';
        let userIds = req.param('user') ? req.param('user').split(",").map(a => +a) : [];
        let sortKey = req.param('sortKey', 'id');
        let sortDir = req.param('sortDir', 'desc');

        let defaultResponse = {records: [], totalCount: 0, q: searchTerm, pageSize, pageNumber, sortKey, sortDir};
        let {
            total: total_record_count,
            records: project_messages
        } = await getProjectSiteMessages(projectId, pageSize, pageNumber * pageSize, sortKey, sortDir, {
            searchTerm,
            userIds
        }, )
        sails.log.info('fetch all messages with filter:', filter);
        //need to refactor this function once other apis are also migrated to use sql function
        project_messages = await expandMessages(project_messages);
        let responseObj = { ...defaultResponse, records: project_messages, totalCount: total_record_count, user_filter_data :[] };
        if (pageNumber == 0 && !searchTerm && !userIds.length) {
            const user_filter_data = await getMessageSendersList('project_site_messaging', 'project_ref', projectId);
            return successResponse(res,{...responseObj,user_filter_data} );
        }
        return successResponse(res, responseObj);
    },

    getCompanyMessagesCA: async(req, res) => {
        return await getCompanyMessagesFn(req, res);
    },
    getCompanyMessagesRecievedByUser: async(req, res) => {
        return await getCompanyMessagesFn(req, res, true);
    },

    getConsolidatedMessagesList: async (req, res) => {
        let userId = +req.user.id;
        let type = req.param('type') || msg_type.received;
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let projectIds = req.param('projects') ? req.param('projects').split(',').map(a=>+a): [];
        let companyIds = req.param('companies') ? req.param('companies').split(',').map(a=>+a): [];
        let searchTerm = (req.param('q') !== null && req.param('q') !== '') ? req.param('q') : '';

        let defaultResponse = {records: [], type, user: userId, projectIds, companyIds, pageSize, pageNumber, searchTerm};

        if(![msg_type.received, msg_type.sent].includes(type)){
            sails.log.info('Invalid request.');
            return errorResponse(res, 'Invalid request.');
        }
       let records = await getConsolidatedMessagesListFn( userId, type, pageSize, pageNumber * pageSize, projectIds, companyIds, searchTerm);
       if(type === msg_type.received){
           records.map(record => {
            record.createdAt = +record.createdAt;
                if(record.source === 'project_message'){
                    let read = record.read_by_recipients.find(a => a.id === userId);
                    record.read_at = read? read.read_at : null;
                }
            })
        }
       return successResponse(res,{...defaultResponse,records});
    },

    getConsolidatedUnreadMessageCount: async ( req, res ) => {
        const userId = +req.user.id;
        let count = await getCombinedUnreadMessagesCountFn(userId);

        return successResponse( res ,{ unreadMessages: +count });
    },

    getUserMessages: async (req, res) => {
        let userId = +req.param('userId');
        let type = req.param('type') || 'received'; //OR sent
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let projectId = +req.param('projectId') || 0;
        let search = (req.query.search || '').toString().trim();

        sails.log.info(`Fetching user ${type} messages.`, `Project Id: ${projectId},`, `Page Number: ${pageNumber},`, `Page Size: ${pageSize}`);

        //fetch all messages sent by the user as userId
        if (type == 'sent') {
            let filter = { user_ref: userId, mark_deleted: false };
            if (search) {
                filter.or = [{msg_title: {contains: search}},{msg_text: {contains: search}}];
            }
            if (projectId) {
                filter.project_ref = projectId;
            }
            sails.log.info('fetch all messages with filter:', filter);
            let total_record_count = await sails.models.projectsitemessaging_reader.count(filter);
            let user_sent_messages = await sails.models.projectsitemessaging_reader.find(filter)
                .sort([
                    {id: 'DESC'}
                ])
                .limit(pageSize)
                .skip(pageNumber * pageSize);
            user_sent_messages = await populateProjectRefs(user_sent_messages, 'project_ref', []);
            user_sent_messages = await populateUserRefs(user_sent_messages, 'user_ref', []);

            user_sent_messages = await expandMessages(user_sent_messages);
            sails.log.info(`Found ${user_sent_messages.length} messages ${type} by the user ${userId}.`);
            return successResponse(res, {user_sent_messages, total_record_count});
        }

        //fetch all messages received by the user as userId
        let searchClause = '';
        if (search) {
            searchClause = `AND (msg_title ILIKE '%${search}%' OR msg_text ILIKE '%${search}%')`;
            sails.log.info(`Received message search: ${searchClause}`);
        }
        let rawTotalCount = [];
        let rawResult = [];
        if (projectId) {
            rawTotalCount = await sails.sendNativeQuery(`SELECT count(id) FROM project_site_messaging WHERE recipients::jsonb @> ($1)::jsonb AND project_ref = $2 AND mark_deleted = false ${searchClause}`,
                [`${userId}`, `${projectId}`]
            );

            rawResult = await sails.sendNativeQuery(`SELECT id, msg_title, msg_text, msg_files, should_accepted, mark_deleted, project_ref, read_by_recipients, accepted_by_recipients, user_ref, "createdAt", "updatedAt" FROM project_site_messaging
                WHERE recipients::jsonb @> ($1)::jsonb AND project_ref = $2 AND mark_deleted = false ${searchClause} ORDER BY id DESC LIMIT $3 OFFSET $4`,
                [`${userId}`, `${projectId}`, `${pageSize}`, `${(pageNumber * pageSize)}`]
            );
        } else {
            rawTotalCount = await sails.sendNativeQuery(`SELECT count(id) FROM project_site_messaging
            WHERE recipients::jsonb @> ($1)::jsonb AND mark_deleted = false ${searchClause}`,
                [`${userId}`]
            );

            rawResult = await sails.sendNativeQuery(`SELECT id, msg_title, msg_text, msg_files, should_accepted, mark_deleted, project_ref, read_by_recipients, accepted_by_recipients, user_ref, "createdAt", "updatedAt" FROM project_site_messaging
            WHERE recipients::jsonb @> ($1)::jsonb AND mark_deleted = false ${searchClause} ORDER BY id DESC LIMIT $2 OFFSET $3`,
                [`${userId}`, `${pageSize}`, `${(pageNumber * pageSize)}`]
            );
        }

        let total_record_count = (HttpService.typeOf(rawTotalCount.rows, 'array') && rawTotalCount.rows.length && rawTotalCount.rows.length && rawTotalCount.rows[0].count) ? rawTotalCount.rows[0].count : 0;
        let user_received_messages = (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        let projectIds = [];
        let msgFileIds = [];
        let userRefIds = [];
        for (let index in user_received_messages) {
            let message = user_received_messages[index];
            user_received_messages[index] = message;

            msgFileIds.push(...message.msg_files);
            projectIds.push(message.project_ref);
            userRefIds.push(message.user_ref);
        }

        projectIds = (projectId) ? [projectId] : projectIds;

        let msgFiles = await sails.models.userfile_reader.find({
            where: {id: _uniq(msgFileIds)},
            select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
        });
        let projectsInfo = await sails.models.project_reader.find({
            where: {id: _uniq(projectIds)},
            select: ['name', 'project_category', 'parent_company', 'project_type', 'main_contact_name', 'main_contact_number']
        });

        let userRefs = await sails.models.user_reader.find({
            where: {id: _uniq([...userRefIds, userId])},
            select: ['first_name', 'middle_name', 'last_name', 'email', 'profile_pic_ref']
        });
        userRefs = await attachProfilePicWithUsersInfo(userRefs, [], true);

        let recipientInfo = userRefs.find(user => user.id == userId);

        //extract info
        for (let index in user_received_messages) {
            let message = user_received_messages[index];
            message.recipient = recipientInfo;
            message.msg_files = msgFiles.filter(file => message.msg_files.includes(file.id));
            message.project_ref = projectsInfo.find(project => project.id == message.project_ref);
            message.user_ref = userRefs.find(user => user.id == message.user_ref);
            user_received_messages[index] = message;
        }

        sails.log.info(`Found ${user_received_messages.length} messages ${type} by the user ${userId}.`);
        return successResponse(res, {user_received_messages, total_record_count});
    },

    getUserSentMessages: async (req, res) => {
        let userId = +req.param('userId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let projectId = +req.param('projectId') || 0;
        let search = (req.query.search || '').toString().trim();

        sails.log.info(`Filter with project: ${(projectId)}`);

        let filter = { user_ref: userId, mark_deleted: false };
        if (projectId) {
            filter.project_ref = projectId;
        }

        if (search) {
            filter.or = [{msg_title: {contains: search}},{msg_text: {contains: search}}];
        }
        sails.log.info('fetch all messages with filter:', filter);
        let total_record_count = await sails.models.projectsitemessaging_reader.count(filter);
        let user_sent_messages = await sails.models.projectsitemessaging_reader.find(filter)
            .sort([
                {id: 'DESC'}
            ])
            .limit(pageSize)
            .skip(pageNumber * pageSize);
        user_sent_messages = await populateProjectRefs(user_sent_messages, 'project_ref', []);
        user_sent_messages = await populateUserRefs(user_sent_messages, 'user_ref', []);

        user_sent_messages = await expandMessages(user_sent_messages);
        sails.log.info(`Found ${user_sent_messages.length} messages sent by the user ${userId}.`);
        return successResponse(res, {user_sent_messages, total_record_count});
    },

    getUserReceivedMessages: async (req, res) => {
        let userId = +req.param('userId');
        let pageSize = +req.param('pageSize', DEFAULT_PAGE_SIZE);
        let pageNumber = +req.param('pageNumber', 0);
        let projectId = +req.param('projectId') || 0;
        let search = (req.query.search || '').toString().trim();

        sails.log.info(`Filter with project: ${(projectId)}`);
        let searchClause = '';
        if (search) {
            searchClause = `AND (msg_title ILIKE '%${search}%' OR msg_text ILIKE '%${search}%')`;
            sails.log.info(`Received message search: ${searchClause}`);
        }

        //fetch all messages received by the user as userId
        let rawTotalCount = [];
        let rawResult = [];
        if (projectId) {
            rawTotalCount = await sails.sendNativeQuery(`SELECT count(id) FROM (
            SELECT id, project_ref, mark_deleted, msg_title, msg_text FROM project_site_messaging
            WHERE recipients::jsonb @> ($1)::jsonb AND project_ref = $2 AND mark_deleted = false) result WHERE project_ref = $2 AND mark_deleted = false ${searchClause}`,
                [`${userId}`, `${projectId}`]
            );

           rawResult = await sails.sendNativeQuery(`SELECT id, msg_title, msg_text, msg_files, should_accepted, mark_deleted, project_ref, read_by_recipients, accepted_by_recipients, user_ref, "createdAt", "updatedAt" FROM (
            SELECT id, msg_title, msg_text, msg_files, should_accepted, mark_deleted, project_ref, read_by_recipients, accepted_by_recipients, user_ref, "createdAt", "updatedAt" FROM project_site_messaging
            WHERE recipients::jsonb @> ($1)::jsonb AND project_ref = $2 AND mark_deleted = false) result WHERE project_ref = $2 AND mark_deleted = false ${searchClause} ORDER BY id DESC LIMIT $3 OFFSET $4`,
                [`${userId}`, `${projectId}`, `${pageSize}`, `${pageNumber}`]
            );

        } else {
            rawTotalCount = await sails.sendNativeQuery(`SELECT count(id) FROM (
            SELECT id, project_ref, mark_deleted, msg_title, msg_text FROM project_site_messaging
            WHERE recipients::jsonb @> ($1)::jsonb AND mark_deleted = false) result WHERE mark_deleted = false ${searchClause}`,
                [`${userId}`]
            );

            rawResult = await sails.sendNativeQuery(`SELECT id, msg_title, msg_text, msg_files, should_accepted, mark_deleted, project_ref, read_by_recipients, accepted_by_recipients, user_ref, "createdAt", "updatedAt" FROM (
            SELECT id, msg_title, msg_text, msg_files, should_accepted, mark_deleted, project_ref, read_by_recipients, accepted_by_recipients, user_ref, "createdAt", "updatedAt" FROM project_site_messaging
            WHERE recipients::jsonb @> ($1)::jsonb AND mark_deleted = false) result WHERE mark_deleted = false ${searchClause} ORDER BY id DESC LIMIT $2 OFFSET $3`,
                [`${userId}`, `${pageSize}`, `${pageNumber}`]
            );
        }

        let total_record_count = (HttpService.typeOf(rawTotalCount.rows, 'array') && rawTotalCount.rows.length && rawTotalCount.rows.length && rawTotalCount.rows[0].count) ? rawTotalCount.rows[0].count : 0;
        let user_received_messages = (HttpService.typeOf(rawResult.rows, 'array') && rawResult.rows.length) ? rawResult.rows : [];
        let projectIds = [];
        let msgFileIds = [];
        let userRefIds = [];
        for (let index in user_received_messages) {
            let message = user_received_messages[index];
            user_received_messages[index] = message;

            msgFileIds.push(...message.msg_files);
            projectIds.push(message.project_ref);
            userRefIds.push(message.user_ref);
        }

        projectIds = (projectId) ? [projectId] : projectIds;

        let msgFiles = await sails.models.userfile_reader.find({
            where: {id: _uniq(msgFileIds)},
            select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
        });
        let projectsInfo = await sails.models.project_reader.find({
            where: {id: _uniq(projectIds)},
            select: ['name', 'project_category', 'parent_company', 'project_type', 'main_contact_name', 'main_contact_number']
        });

        let userRefs = await sails.models.user_reader.find({
            where: {id: _uniq([...userRefIds, userId])},
            select: ['first_name', 'middle_name', 'last_name', 'email', 'profile_pic_ref']
        });
        userRefs = await attachProfilePicWithUsersInfo(userRefs, [], true);

        let recipientInfo = userRefs.find(user => user.id == userId);

        //extract info
        for (let index in user_received_messages) {
            let message = user_received_messages[index];
            message.recipient = recipientInfo;
            message.msg_files = msgFiles.filter(file => message.msg_files.includes(file.id));
            message.project_ref = projectsInfo.find(project => project.id == message.project_ref);
            message.user_ref = userRefs.find(user => user.id == message.user_ref);
            user_received_messages[index] = message;
        }

        sails.log.info(`Found ${user_received_messages.length} messages received by the user ${userId}.`);
        return successResponse(res, {user_received_messages, total_record_count});
    },

    getMessage: async (req, res) => {
        let messageId = +req.param('messageId');
        let projectId = +req.param('projectId', 0);
        sails.log.info('Get message data by message id: ', messageId);

        if (!messageId || !projectId) {
            sails.log.info('Invalid request.');
            return errorResponse(res, 'message_id  and projectId are required param');
        };

        let message_data = await sails.models.projectsitemessaging_reader.findOne({ id: messageId, project_ref: projectId });

        if(!message_data) {
            sails.log.info(`Failed to find message with id: ${messageId}`);
            return errorResponse(res, `Failed to find message with id: ${messageId}`);
        }
        message_data.user_ref = await sails.models.user_reader.findOne({
            where: {id: message_data.user_ref},
            select: ['first_name', 'middle_name', 'last_name', 'email', 'profile_pic_ref']
        }).populate('profile_pic_ref');

        message_data.project_ref = await sails.models.project_reader.findOne({
            where: {id: message_data.project_ref},
            select: ['name', 'project_category', 'parent_company', 'project_type', 'main_contact_name', 'main_contact_number','contractor']
        });

        message_data.recipients = await sails.models.user_reader.find({
            where: { id: message_data.recipients},
            select: ['id','first_name', 'middle_name', 'last_name', 'profile_pic_ref']
        });

        let recipientsProfilePic = message_data.recipients.map(u => u.profile_pic_ref).filter(a => a !== null );

        let filesInfo = await sails.models.userfile_reader.find({
                where: {id: [..._uniq(message_data.msg_files), ..._uniq(recipientsProfilePic)]},
                select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
            });

        const fileMap = new Map(filesInfo.map(f => [f.id, f]));
        if(message_data.msg_files.length){
            message_data.msg_files = message_data.msg_files.map(f => fileMap.get(f));
        }
        message_data.recipients = message_data.recipients.map(u => ({ ...u.toJSON(), profile_pic_ref: fileMap.get(u.profile_pic_ref) || null }));
        sails.log.info(`Found message with id: ${messageId}`);
        return successResponse(res, {message_data});
    },

    getUserMessagesCount: async (req, res) => {
        let projectId = +req.param('projectId');
        let count_all = req.param('all', 'unread') === 'all';

        sails.log.info(`get user messages count user: ${req.user.id}, all: ${count_all}`);
        let readByFilter = (count_all) ? '' : `AND NOT (read_by_recipients::JSONB @> jsonb_build_array(jsonb_build_object('id', ${req.user.id}::int)))`;
        let rawTotalCount = await sails.sendNativeQuery(`
            SELECT count(id) FROM project_site_messaging WHERE project_ref = $1 AND recipients::jsonb @> ($2)::jsonb ${readByFilter}`,
            [projectId, req.user.id]
        );

        let count = (HttpService.typeOf(rawTotalCount.rows, 'array') && rawTotalCount.rows.length && rawTotalCount.rows[0].count) ? rawTotalCount.rows[0].count : 0;

        return successResponse(res, {count});
    },

    sendCompanyMessage: sendCompanyMessageFn,
    editCompanyMessage: sendCompanyMessageFn,
    deleteCompanyMessage: async (req, res) => {
        let employer_id = +req.param('employerId',0);
        let {
            message_id,
        } = _.pick((req.body || {}), [
            'message_id',
        ]);
        let today = dayjs().unix();
        sails.log.info(`delete company message: ${message_id}, deleted_on: ${today} `);
        const result = await sails.models.sitemessage.updateOne({id: message_id, company_ref: employer_id}).set({deleted_on:today})

        if (result.deleted_on) {
            return successResponse(res, {
                message: "message deleted successfully"
            });
        };
        return errorResponse(res, `Unable to delete the message`);
    },
    markAsCompanyMessage: async (req, res) => {
        let message_id = +req.param('id', 0)
        let markAs = req.param('markas');
        let today = dayjs().unix();
        if (!['read', 'accepted'].includes(markAs)) {
            sails.log.info('Invalid request.');
            return errorResponse(res, 'Invalid request.');
        };
        let updatedKey = (markAs === 'read')?'read_at':'accepted_at';

        let record = await sails.models.sitemessagerecipient.updateOne({
                msg_ref: message_id,
                user_ref: req.user.id
            }).set({[updatedKey]: today});

        if(record){
            return successResponse(res, {message: `this message has ${markAs} successfully.`});
        }
        return errorResponse(res,{message:"error updating the message."})

    },
    getCompanyMessage: async (req, res) => {
        let message_id = req.param('messageId', 0);
        let employer_id = +req.param('employerId',0);
        let type = req.param('type') || msg_type.received;
        if (!message_id || !employer_id) {
            sails.log.info('Invalid request.');
            return errorResponse(res, 'message_id  and employer_id are required param');
        }
        if(![msg_type.received, msg_type.sent].includes(type)){
            sails.log.info('Invalid request.');
            return errorResponse(res, 'Invalid request.');
        }
        let messageInfo = await sails.models.sitemessage_reader.findOne({
            where: {
                id: +message_id,
                company_ref: employer_id,
            }
        });
        if(!messageInfo){
            return errorResponse(res,{message:"Something went wrong. Error retrieving message details."})
        }
        messageInfo.projects = await sails.models.project_reader.find({
            where: {id: messageInfo.projects},
            select: ['id','name', 'project_category', 'parent_company']
        });

        messageInfo.sender_ref = await sails.models.user_reader.findOne({
            where: {id: messageInfo.sender_ref},
            select: ['first_name', 'middle_name', 'last_name', 'email', 'profile_pic_ref']
        }).populate('profile_pic_ref');

        if (messageInfo.msg_files.length) {
            messageInfo.msg_files = await sails.models.userfile_reader.find({
                where: {id: [..._uniq(messageInfo.msg_files)]},
                select: ['id', 'name', 'sm_url', 'md_url', 'file_url', 'img_translation', 'file_mime']
            });
        };
        let recipiendInfo
        if(type === msg_type.received ){
            recipiendInfo = await sails.models.sitemessagerecipient_reader.findOne({
                where: {
                    user_ref: req.user.id,
                    msg_ref: message_id
                }
            });
            if(!recipiendInfo){
                return errorResponse(res,{message:"Something went wrong. Error retrieving recipient details."})
            }
        }
        let recipients =  await getCompanyMessageRecipientsDetailsFn(message_id, false, true, type, req.user.id);
        messageInfo = {
            ...messageInfo,
            recipient_info: recipiendInfo,
            recipients
        }
        return successResponse(res, {messageInfo});
    },
    getCompanyMessageRecipientsDetails:async(req, res)=>{
        let message_id = req.param('messageId', 0);
        let employer_id = +req.param('employerId',0);
        if (!message_id) {
            return errorResponse(res, 'message_id is a required param');
        }
        let {deliveredTo, readBy} =  await getCompanyMessageRecipientsDetailsFn(message_id, true, false, null, null);
        return successResponse(res,{deliveredTo, readBy} );
    },
    resendCompanyMessage: async (req, res) => {
        let message_id = req.param('messageId', 0);
        let employer_id = req.param('employerId', 0);
        const userIds = req.body.userIds ? req.body.userIds : [];
        if(!userIds.length){
            return errorResponse(res, {
                message: 'Invalid Request. user ids are required.'
            });
        }
        let employer = await sails.models.createemployer_reader.findOne({
            id: employer_id
        }).select(['id', 'name']);
        const messageInfo = await sails.models.sitemessage.findOne({
            id: message_id,
            company_ref: employer_id,
        });
        if (!messageInfo) {
            return errorResponse(res, {
                message: 'no record found for provided message Id.'
            });
        }
        const recipients = await getRecipientsList(message_id, userIds, false, null);
        if(!recipients.length){
            return errorResponse(res,{message: "something went wrong, error fetching recipients data"});
        }
        await sendMessageAsPushNotificationV2(messageInfo, {}, req.user, recipients.map(u => u.id), employer);
        let today = dayjs().unix();
        let updatedData = await updateResentData(message_id, userIds, req.user, today);
        if(!updatedData){
            return errorResponse(res,{message:"something went wrong, message resend failed."});
        }
        return successResponse(res,{message:`resent message to ${updatedData.length} users`});
    }
};
